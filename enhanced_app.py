"""
Enhanced Hugging Face Spaces App for VGG-inspired Vision Transformers
Comprehensive web interface with advanced features and real-time monitoring
"""

import gradio as gr
import torch
import torch.nn.functional as F
import numpy as np
import json
import time
import io
import base64
import os
import psutil
import threading
from PIL import Image
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Any
import pandas as pd
from datetime import datetime
import cv2
import tempfile
import zipfile
from pathlib import Path

from hybrid_vgg_vit import (
    create_hybrid_vgg_vit, 
    create_model_from_config,
    get_recommended_configs,
    HybridVGGViT
)
from model_utils import ModelAnalyzer, InferenceUtils

# Global variables for model management
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
MODELS = {}
CURRENT_MODEL = None
CURRENT_CONFIG = None
CURRENT_CHECKPOINT = None

# ImageNet class names (subset for demo)
with open('imagenet_classes.json', 'r') as f:
    IMAGENET_CLASSES = json.load(f)

# Create examples directory if it doesn't exist
os.makedirs('examples', exist_ok=True)

class ModelInterface:
    """Enhanced model interface with comprehensive functionality"""
    
    def __init__(self):
        self.device = DEVICE
        self.models = MODELS
        self.current_model = None
        self.current_config = None
        self.analyzer = None
        
    def load_model(self, config_name: str, checkpoint_path: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced model loading with comprehensive error handling"""
        try:
            if config_name in self.models and checkpoint_path is None:
                self.current_model = self.models[config_name]
                self.current_config = config_name
                return {
                    "status": "success",
                    "message": f"Model '{config_name}' loaded from cache",
                    "model_info": self.get_model_info()
                }
            
            # Create model from config
            model = create_model_from_config(config_name, num_classes=1000)
            
            # Load checkpoint if provided
            if checkpoint_path and os.path.exists(checkpoint_path):
                checkpoint = torch.load(checkpoint_path, map_location=self.device)
                model.load_state_dict(checkpoint['model_state_dict'])
                CURRENT_CHECKPOINT = checkpoint_path
            elif checkpoint_path:
                return {
                    "status": "error",
                    "message": f"Checkpoint file not found: {checkpoint_path}",
                    "model_info": None
                }
            
            model = model.to(self.device)
            model.eval()
            
            # Cache the model
            self.models[config_name] = model
            self.current_model = model
            self.current_config = config_name
            self.analyzer = ModelAnalyzer(model, self.device)
            
            return {
                "status": "success",
                "message": f"Model '{config_name}' loaded successfully",
                "model_info": self.get_model_info()
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Error loading model: {str(e)}",
                "model_info": None
            }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information"""
        if self.current_model is None:
            return {"error": "No model loaded"}
        
        analyzer = ModelAnalyzer(self.current_model, self.device)
        complexity = analyzer.analyze_model_complexity()
        
        return {
            "config": self.current_config,
            "parameters": complexity['parameters'],
            "model_size_mb": complexity['model_size_mb'],
            "input_shape": complexity['input_shape'],
            "output_shape": complexity['output_shape'],
            "device": str(self.device),
            "vgg_blocks": 4,
            "transformer_layers": len(self.current_model.transformer_blocks),
            "embedding_dim": self.current_model.norm.normalized_shape[0]
        }
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system resource information"""
        memory = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=1)
        
        gpu_info = {}
        if torch.cuda.is_available():
            gpu_info = {
                "gpu_name": torch.cuda.get_device_name(0),
                "gpu_memory_total": torch.cuda.get_device_properties(0).total_memory / (1024**3),
                "gpu_memory_used": torch.cuda.memory_allocated(0) / (1024**3),
                "gpu_memory_free": torch.cuda.memory_reserved(0) / (1024**3)
            }
        
        return {
            "cpu_usage": cpu_percent,
            "memory_total": memory.total / (1024**3),
            "memory_used": memory.used / (1024**3),
            "memory_available": memory.available / (1024**3),
            "memory_percent": memory.percent,
            "gpu_info": gpu_info
        }

# Initialize interface
interface = ModelInterface()

def preprocess_image(image: Image.Image, size: Tuple[int, int] = (224, 224)) -> torch.Tensor:
    """Enhanced image preprocessing with validation"""
    if image is None:
        raise ValueError("No image provided")
    
    # Convert to RGB if necessary
    if image.mode != 'RGB':
        image = image.convert('RGB')
    
    # Resize
    image = image.resize(size, Image.Resampling.LANCZOS)
    
    # Convert to tensor and normalize
    img_array = np.array(image).astype(np.float32) / 255.0
    img_tensor = torch.from_numpy(img_array).permute(2, 0, 1)
    
    # ImageNet normalization
    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
    img_tensor = (img_tensor - mean) / std
    
    return img_tensor.unsqueeze(0)

def predict_single_image(
    image: Image.Image,
    confidence_threshold: float = 0.01,
    top_k: int = 5
) -> Dict[str, Any]:
    """Enhanced single image prediction with comprehensive results"""
    if interface.current_model is None:
        return {"error": "No model loaded"}
    
    if image is None:
        return {"error": "No image provided"}
    
    start_time = time.time()
    
    try:
        # Preprocess image
        input_tensor = preprocess_image(image).to(DEVICE)
        
        # Make prediction
        with torch.no_grad():
            outputs = interface.current_model(input_tensor)
            probabilities = F.softmax(outputs, dim=1)
            features = interface.current_model.get_feature_maps(input_tensor)
        
        inference_time = (time.time() - start_time) * 1000
        
        # Get top predictions
        top_probs, top_indices = torch.topk(probabilities[0], min(top_k, 1000))
        
        # Format results
        predictions = []
        for prob, idx in zip(top_probs, top_indices):
            prob_value = prob.item()
            if prob_value >= confidence_threshold:
                class_name = IMAGENET_CLASSES.get(str(idx.item()), f"Class_{idx.item()}")
                predictions.append({
                    "class": class_name,
                    "confidence": prob_value,
                    "index": idx.item()
                })
        
        # Create visualizations
        attention_map = create_attention_visualization(features)
        feature_viz = create_feature_visualization(features)
        
        return {
            "predictions": predictions,
            "inference_time_ms": inference_time,
            "model": interface.current_config,
            "device": str(DEVICE),
            "image_shape": image.size,
            "status": "success"
        }
        
    except Exception as e:
        return {
            "error": str(e),
            "status": "error"
        }

def create_attention_visualization(features: torch.Tensor) -> Image.Image:
    """Create enhanced attention/activation heatmap"""
    # Average across channels to get spatial attention
    attention = features.mean(dim=1)[0].cpu().numpy()
    
    # Normalize to 0-1
    attention = (attention - attention.min()) / (attention.max() - attention.min() + 1e-8)
    
    # Create heatmap
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Spatial attention
    ax1.imshow(attention, cmap='hot', interpolation='bilinear')
    ax1.set_title('Spatial Attention Map')
    ax1.axis('off')
    
    # Histogram of activations
    ax2.hist(attention.flatten(), bins=50, alpha=0.7, color='red')
    ax2.set_title('Activation Distribution')
    ax2.set_xlabel('Activation Strength')
    ax2.set_ylabel('Frequency')
    
    plt.tight_layout()
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def create_feature_visualization(features: torch.Tensor, num_features: int = 16) -> Image.Image:
    """Enhanced feature map visualization with statistics"""
    feature_maps = features[0].cpu().numpy()
    
    # Select top features by activation strength
    activation_strengths = feature_maps.mean(axis=(1, 2))
    top_indices = np.argsort(activation_strengths)[-num_features:][::-1]
    
    # Create grid visualization
    grid_size = int(np.ceil(np.sqrt(num_features)))
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(12, 12))
    axes = axes.flatten()
    
    for i, idx in enumerate(top_indices):
        if i < len(axes):
            feature_map = feature_maps[idx]
            # Normalize
            feature_map = (feature_map - feature_map.min()) / (feature_map.max() - feature_map.min() + 1e-8)
            im = axes[i].imshow(feature_map, cmap='viridis')
            axes[i].set_title(f'F{idx} (μ={activation_strengths[idx]:.3f})')
            axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(len(top_indices), len(axes)):
        axes[i].axis('off')
    
    plt.suptitle('Top Feature Maps by Activation Strength', fontsize=14)
    plt.tight_layout()
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def batch_predict(
    images: List[Image.Image],
    confidence_threshold: float = 0.01,
    top_k: int = 5
) -> pd.DataFrame:
    """Enhanced batch processing with progress tracking"""
    if interface.current_model is None:
        return pd.DataFrame({"error": ["No model loaded"]})
    
    results = []
    
    for idx, image in enumerate(images):
        if image is not None:
            result = predict_single_image(image, confidence_threshold, top_k)
            if "predictions" in result:
                for pred in result["predictions"]:
                    results.append({
                        "image_index": idx + 1,
                        "class": pred["class"],
                        "confidence": pred["confidence"],
                        "inference_time_ms": result["inference_time_ms"]
                    })
    
    return pd.DataFrame(results)

def export_results(results: Dict, format: str = "json") -> Tuple[str, str]:
    """Enhanced export functionality with multiple formats"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if format == "json":
        filename = f"predictions_{timestamp}.json"
        content = json.dumps(results, indent=2)
    elif format == "csv":
        filename = f"predictions_{timestamp}.csv"
        if isinstance(results, pd.DataFrame):
            content = results.to_csv(index=False)
        else:
            df = pd.DataFrame(results.get("predictions", []))
            content = df.to_csv(index=False)
    elif format == "excel":
        filename = f"predictions_{timestamp}.xlsx"
        # Excel export would require openpyxl
        content = "Excel format requires openpyxl library"
    else:
        filename = f"predictions_{timestamp}.txt"
        content = str(results)
    
    return content, filename

def create_model_comparison_plot() -> Image.Image:
    """Create comprehensive model comparison visualization"""
    configs = get_recommended_configs()
    
    # Extract data for plotting
    data = []
    for config_name, config in configs.items():
        # Estimate parameters based on config
        embed_dim = config["embed_dim"]
        num_layers = config["num_transformer_layers"]
        
        # VGG parameters (approximately)
        vgg_params = 15_000_000
        
        # Transformer parameters (approximately)
        transformer_params = num_layers * (
            4 * embed_dim * embed_dim +  # Attention
            2 * embed_dim * embed_dim * 4  # MLP
        )
        
        # Classification head
        head_params = embed_dim * 1000
        
        total_params = vgg_params + transformer_params + head_params
        
        data.append({
            "Model": config_name.capitalize(),
            "Parameters (M)": total_params / 1e6,
            "Embedding Dim": config["embed_dim"],
            "Transformer Layers": config["num_transformer_layers"],
            "Estimated Size (MB)": total_params * 4 / (1024 * 1024)
        })
    
    df = pd.DataFrame(data)
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Parameters comparison
    axes[0, 0].bar(df["Model"], df["Parameters (M)"], color='skyblue')
    axes[0, 0].set_ylabel("Parameters (Millions)")
    axes[0, 0].set_title("Model Size Comparison")
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Embedding dimension
    axes[0, 1].bar(df["Model"], df["Embedding Dim"], color='lightgreen')
    axes[0, 1].set_ylabel("Embedding Dimension")
    axes[0, 1].set_title("Embedding Dimension by Model")
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # Transformer layers
    axes[1, 0].bar(df["Model"], df["Transformer Layers"], color='salmon')
    axes[1, 0].set_ylabel("Number of Layers")
    axes[1, 0].set_title("Transformer Layers by Model")
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    # Memory usage
    axes[1, 1].bar(df["Model"], df["Estimated Size (MB)"], color='gold')
    axes[1, 1].set_ylabel("Memory Usage (MB)")
    axes[1, 1].set_title("Estimated Memory Usage")
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    plt.suptitle('VGG-ViT Model Comparison', fontsize=16)
    plt.tight_layout()
    
    # Convert to image
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=150)
    buf.seek(0)
    plt.close()
    
    return Image.open(buf)

def create_system_monitor() -> Dict[str, Any]:
    """Create real-time system monitoring data"""
    return interface.get_system_info()

# Create enhanced Gradio interface
def create_enhanced_interface():
    """Create comprehensive enhanced interface"""
    
    with gr.Blocks(title="Enhanced VGG-ViT Vision Transformer", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🤖 Enhanced VGG-ViT Vision Transformer
        
        **Advanced Features:**
        - 🎯 Multiple model variants with checkpoint support
        - 📊 Real-time system monitoring
        - 📈 Advanced visualizations and analytics
        - 🔄 Batch processing with progress tracking
        - 💾 Comprehensive export options
        - 🎛️ Hyperparameter tuning interface
        - 📁 Dataset management tools
        """)
        
        # State management
        model_state = gr.State({"model": None, "config": None})
        
        with gr.Tabs():
            
            # Model Management Tab
            with gr.TabItem("🔧 Model Management"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### Model Configuration")
                        
                        model_selector = gr.Dropdown(
                            choices=["tiny", "small", "base", "large"],
                            value="small",
                            label="Select Model Variant",
                            info="Choose model size based on your requirements"
                        )
                        
                        checkpoint_input = gr.Textbox(
                            label="Checkpoint Path (Optional)",
                            placeholder="path/to/checkpoint.pth",
                            info="Load pre-trained weights"
                        )
                        
                        with gr.Row():
                            load_btn = gr.Button("🚀 Load Model", variant="primary")
                            refresh_btn = gr.Button("🔄 Refresh")
                        
                        load_status = gr.JSON(label="Loading Status")
                        
                        gr.Markdown("### Available Configurations")
                        configs = get_recommended_configs()
                        for name, config in configs.items():
