"""
Comprehensive testing and debugging utilities for Hybrid VGG-ViT model.

This module provides utilities for:
1. Model configuration validation
2. Dimension compatibility testing
3. Performance benchmarking
4. Error diagnosis and troubleshooting
"""

import torch
import torch.nn as nn
import time
import traceback
import warnings
from typing import Dict, List, Tuple, Any, Optional

from hybrid_vgg_vit import (
    create_hybrid_vgg_vit, 
    get_recommended_configs,
    create_model_from_config,
    validate_model_config,
    debug_tensor_shapes,
    ConfigurationError,
    DimensionError
)


class ModelTester:
    """
    Comprehensive model testing and debugging utility.
    """
    
    def __init__(self, device: Optional[torch.device] = None):
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.test_results = {}
        
    def test_model_creation(self, config: Dict[str, Any]) -> bool:
        """
        Test model creation with given configuration.
        
        Args:
            config: Model configuration dictionary
            
        Returns:
            True if successful, False otherwise
        """
        try:
            print(f"Testing model creation with config: {config}")
            
            model = create_hybrid_vgg_vit(**config, validate_config=True)
            param_count = sum(p.numel() for p in model.parameters())
            
            print(f"✓ Model created successfully with {param_count:,} parameters")
            return True
            
        except (ConfigurationError, DimensionError) as e:
            print(f"✗ Configuration error: {e}")
            return False
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            traceback.print_exc()
            return False
    
    def test_forward_pass(self, model: nn.Module, batch_sizes: List[int] = [1, 4, 8]) -> Dict[str, Any]:
        """
        Test forward pass with different batch sizes.
        
        Args:
            model: Model to test
            batch_sizes: List of batch sizes to test
            
        Returns:
            Dictionary with test results
        """
        model.to(self.device)
        model.eval()
        
        results = {}
        
        for batch_size in batch_sizes:
            try:
                print(f"Testing forward pass with batch_size={batch_size}")
                
                # Create input tensor
                input_tensor = torch.randn(batch_size, 3, 224, 224).to(self.device)
                
                # Measure inference time
                with torch.no_grad():
                    # Warmup
                    for _ in range(3):
                        _ = model(input_tensor)
                    
                    # Actual timing
                    if self.device.type == 'cuda':
                        torch.cuda.synchronize()
                        start_time = torch.cuda.Event(enable_timing=True)
                        end_time = torch.cuda.Event(enable_timing=True)
                        
                        start_time.record()
                        output = model(input_tensor)
                        end_time.record()
                        
                        torch.cuda.synchronize()
                        inference_time = start_time.elapsed_time(end_time)
                    else:
                        start_time = time.time()
                        output = model(input_tensor)
                        end_time = time.time()
                        inference_time = (end_time - start_time) * 1000
                
                # Validate output
                expected_output_shape = (batch_size, model.head.out_features)
                if output.shape != expected_output_shape:
                    raise DimensionError(
                        f"Output shape mismatch: expected {expected_output_shape}, got {output.shape}"
                    )
                
                results[batch_size] = {
                    'success': True,
                    'inference_time_ms': inference_time,
                    'output_shape': tuple(output.shape),
                    'output_range': (output.min().item(), output.max().item())
                }
                
                print(f"✓ Batch size {batch_size}: {inference_time:.2f}ms, output shape {output.shape}")
                
            except Exception as e:
                results[batch_size] = {
                    'success': False,
                    'error': str(e),
                    'error_type': type(e).__name__
                }
                print(f"✗ Batch size {batch_size} failed: {e}")
        
        return results
    
    def test_memory_usage(self, model: nn.Module, max_batch_size: int = 32) -> Dict[str, Any]:
        """
        Test memory usage with increasing batch sizes.
        
        Args:
            model: Model to test
            max_batch_size: Maximum batch size to test
            
        Returns:
            Dictionary with memory usage results
        """
        if self.device.type != 'cuda':
            print("Memory testing only available on CUDA devices")
            return {}
        
        model.to(self.device)
        model.eval()
        
        results = {}
        
        for batch_size in [1, 2, 4, 8, 16, max_batch_size]:
            try:
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                
                input_tensor = torch.randn(batch_size, 3, 224, 224).to(self.device)
                
                with torch.no_grad():
                    output = model(input_tensor)
                
                memory_used = torch.cuda.max_memory_allocated() / 1024**3  # GB
                
                results[batch_size] = {
                    'success': True,
                    'memory_gb': memory_used,
                    'memory_per_sample_mb': (memory_used * 1024) / batch_size
                }
                
                print(f"✓ Batch size {batch_size}: {memory_used:.2f} GB total, "
                      f"{(memory_used * 1024) / batch_size:.1f} MB per sample")
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    results[batch_size] = {
                        'success': False,
                        'error': 'CUDA out of memory',
                        'max_safe_batch_size': batch_size // 2 if batch_size > 1 else 1
                    }
                    print(f"✗ Batch size {batch_size}: CUDA out of memory")
                    break
                else:
                    results[batch_size] = {
                        'success': False,
                        'error': str(e)
                    }
                    print(f"✗ Batch size {batch_size}: {e}")
            except Exception as e:
                results[batch_size] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"✗ Batch size {batch_size}: {e}")
        
        return results
    
    def test_all_configurations(self) -> Dict[str, Dict[str, Any]]:
        """
        Test all recommended model configurations.
        
        Returns:
            Dictionary with test results for each configuration
        """
        configs = get_recommended_configs()
        results = {}
        
        print("Testing all recommended configurations...")
        print("=" * 60)
        
        for config_name, config_params in configs.items():
            print(f"\nTesting '{config_name}' configuration:")
            print(f"Description: {config_params['description']}")
            
            # Test model creation
            creation_success = self.test_model_creation({
                'num_classes': 1000,
                'embed_dim': config_params['embed_dim'],
                'num_heads': config_params['num_heads'],
                'num_transformer_layers': config_params['num_transformer_layers']
            })
            
            if creation_success:
                try:
                    # Create model for further testing
                    model = create_model_from_config(config_name, num_classes=1000)
                    
                    # Test forward pass
                    forward_results = self.test_forward_pass(model, batch_sizes=[1, 4])
                    
                    # Test memory usage (if CUDA available)
                    memory_results = {}
                    if self.device.type == 'cuda':
                        memory_results = self.test_memory_usage(model, max_batch_size=16)
                    
                    results[config_name] = {
                        'creation_success': True,
                        'forward_pass': forward_results,
                        'memory_usage': memory_results,
                        'parameters': sum(p.numel() for p in model.parameters())
                    }
                    
                except Exception as e:
                    results[config_name] = {
                        'creation_success': True,
                        'testing_error': str(e)
                    }
            else:
                results[config_name] = {
                    'creation_success': False
                }
        
        return results
    
    def diagnose_configuration_error(self, embed_dim: int, num_heads: int) -> List[str]:
        """
        Diagnose configuration errors and provide suggestions.
        
        Args:
            embed_dim: Embedding dimension
            num_heads: Number of attention heads
            
        Returns:
            List of diagnostic messages and suggestions
        """
        suggestions = []
        
        if embed_dim % num_heads != 0:
            suggestions.append(
                f"ERROR: embed_dim ({embed_dim}) must be divisible by num_heads ({num_heads})"
            )
            
            # Suggest valid combinations
            valid_combinations = []
            for heads in [6, 8, 12, 16]:
                for dim in [384, 512, 768, 1024]:
                    if dim % heads == 0:
                        valid_combinations.append((dim, heads))
            
            suggestions.append("Valid combinations (embed_dim, num_heads):")
            for dim, heads in valid_combinations[:8]:  # Show first 8
                suggestions.append(f"  - embed_dim={dim}, num_heads={heads}")
        
        if embed_dim < 64:
            suggestions.append(f"WARNING: Very small embed_dim ({embed_dim}) may hurt performance")
        
        if embed_dim > 2048:
            suggestions.append(f"WARNING: Large embed_dim ({embed_dim}) may cause memory issues")
        
        if num_heads > 32:
            suggestions.append(f"WARNING: Many attention heads ({num_heads}) may be inefficient")
        
        return suggestions


def run_comprehensive_tests():
    """Run comprehensive model tests and generate report."""
    print("Hybrid VGG-ViT Model Comprehensive Testing")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Testing on device: {device}")
    
    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    tester = ModelTester(device)
    
    # Test all configurations
    results = tester.test_all_configurations()
    
    # Generate summary report
    print("\n" + "=" * 60)
    print("SUMMARY REPORT")
    print("=" * 60)
    
    for config_name, result in results.items():
        print(f"\n{config_name.upper()} Configuration:")
        if result['creation_success']:
            print(f"  ✓ Model creation: SUCCESS")
            if 'parameters' in result:
                print(f"  ✓ Parameters: {result['parameters']:,}")
            
            if 'forward_pass' in result:
                successful_batches = sum(1 for r in result['forward_pass'].values() if r['success'])
                total_batches = len(result['forward_pass'])
                print(f"  ✓ Forward pass: {successful_batches}/{total_batches} batch sizes successful")
            
            if 'memory_usage' in result and result['memory_usage']:
                max_batch = max(bs for bs, r in result['memory_usage'].items() if r['success'])
                print(f"  ✓ Max batch size: {max_batch}")
        else:
            print(f"  ✗ Model creation: FAILED")
    
    print(f"\nTesting completed on {device}")
    return results


if __name__ == "__main__":
    run_comprehensive_tests()
