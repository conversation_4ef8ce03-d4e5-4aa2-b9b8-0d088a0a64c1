# Core deep learning framework - exact versions for reproducibility
torch==2.1.0
torchvision==0.16.0

# Data manipulation and numerical computing
numpy==1.24.3
pandas==2.0.3

# Visualization
matplotlib==3.7.2
seaborn==0.12.2

# Image processing
Pillow==10.0.0
opencv-python==********

# Progress bars and utilities
tqdm==4.66.1

# Scientific computing
scipy==1.11.1

# Optional: For advanced features (uncomment if needed)
# tensorboard==2.14.0  # For training visualization
# wandb==0.15.8        # For experiment tracking
# timm==0.9.7          # For additional model components
# einops==0.6.1        # For tensor operations

# Development and testing (uncomment for development)
# pytest==7.4.0
# black==23.7.0
# flake8==6.0.0
# mypy==1.5.1

# Platform-specific installations
# For CUDA support (choose appropriate version):
# torch==2.1.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html
# torchvision==0.16.0+cu118 -f https://download.pytorch.org/whl/torch_stable.html

# For CPU-only installation:
# torch==2.1.0+cpu -f https://download.pytorch.org/whl/torch_stable.html
# torchvision==0.16.0+cpu -f https://download.pytorch.org/whl/torch_stable.html
