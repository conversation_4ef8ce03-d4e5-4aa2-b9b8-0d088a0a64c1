"""
Setup and installation test script for Hybrid VGG-ViT model.

This script validates that all dependencies are correctly installed
and the model can be created and used without errors.
"""

import sys
import importlib
import warnings
from typing import List, Tuple, Dict, Any


def check_python_version() -> bool:
    """Check if Python version is compatible."""
    min_version = (3, 8)
    current_version = sys.version_info[:2]
    
    if current_version >= min_version:
        print(f"✓ Python version: {sys.version.split()[0]} (compatible)")
        return True
    else:
        print(f"✗ Python version: {sys.version.split()[0]} (requires >= {min_version[0]}.{min_version[1]})")
        return False


def check_dependencies() -> Tuple[List[str], List[str]]:
    """Check if all required dependencies are installed."""
    required_packages = [
        'torch',
        'torchvision', 
        'numpy',
        'matplotlib',
        'PIL',
        'tqdm',
        'scipy'
    ]
    
    installed = []
    missing = []
    
    for package in required_packages:
        try:
            if package == 'PIL':
                importlib.import_module('PIL')
            else:
                importlib.import_module(package)
            installed.append(package)
            print(f"✓ {package}: installed")
        except ImportError:
            missing.append(package)
            print(f"✗ {package}: missing")
    
    return installed, missing


def check_torch_installation() -> Dict[str, Any]:
    """Check PyTorch installation details."""
    try:
        import torch
        
        info = {
            'version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': torch.version.cuda if torch.cuda.is_available() else None,
            'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
        
        print(f"✓ PyTorch version: {info['version']}")
        
        if info['cuda_available']:
            print(f"✓ CUDA available: version {info['cuda_version']}")
            print(f"✓ GPU devices: {info['device_count']}")
            for i in range(info['device_count']):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
                print(f"  - GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
        else:
            print("⚠ CUDA not available (CPU-only mode)")
        
        return info
        
    except ImportError:
        print("✗ PyTorch not installed")
        return {}


def test_model_creation() -> bool:
    """Test basic model creation and functionality."""
    try:
        print("\nTesting model creation...")
        
        # Import our modules
        from hybrid_vgg_vit import create_hybrid_vgg_vit, get_recommended_configs
        
        # Test recommended configurations
        configs = get_recommended_configs()
        print(f"✓ Found {len(configs)} recommended configurations")
        
        # Test creating a small model
        print("Creating small test model...")
        model = create_hybrid_vgg_vit(
            num_classes=10,
            embed_dim=384,
            num_heads=6,
            num_transformer_layers=2,
            validate_config=True
        )
        
        param_count = sum(p.numel() for p in model.parameters())
        print(f"✓ Model created with {param_count:,} parameters")
        
        # Test forward pass
        import torch
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model.to(device)
        model.eval()
        
        # Test with small input
        test_input = torch.randn(2, 3, 224, 224).to(device)
        
        with torch.no_grad():
            output = model(test_input)
        
        expected_shape = (2, 10)
        if output.shape == expected_shape:
            print(f"✓ Forward pass successful: {test_input.shape} -> {output.shape}")
            return True
        else:
            print(f"✗ Forward pass failed: expected {expected_shape}, got {output.shape}")
            return False
            
    except Exception as e:
        print(f"✗ Model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_training_utilities() -> bool:
    """Test training utilities and data loading."""
    try:
        print("\nTesting training utilities...")
        
        from train_hybrid_model import create_sample_data_loaders
        
        # Test sample data creation
        train_loader, val_loader = create_sample_data_loaders(
            num_classes=5,
            train_samples=50,
            val_samples=10,
            batch_size=4
        )
        
        print(f"✓ Sample data loaders created: {len(train_loader)} train, {len(val_loader)} val batches")
        
        # Test data loading
        for batch_idx, (data, target) in enumerate(train_loader):
            if batch_idx == 0:  # Test first batch only
                print(f"✓ Data batch shape: {data.shape}, target shape: {target.shape}")
                break
        
        return True
        
    except Exception as e:
        print(f"✗ Training utilities test failed: {e}")
        return False


def test_analysis_tools() -> bool:
    """Test model analysis and utility tools."""
    try:
        print("\nTesting analysis tools...")
        
        from model_utils import ModelAnalyzer
        from hybrid_vgg_vit import create_hybrid_vgg_vit
        import torch
        
        # Create a small model for testing
        model = create_hybrid_vgg_vit(
            num_classes=10,
            embed_dim=192,
            num_heads=3,
            num_transformer_layers=2
        )
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        analyzer = ModelAnalyzer(model, device)
        
        # Test parameter counting
        param_stats = analyzer.count_parameters()
        print(f"✓ Parameter analysis: {param_stats['total']:,} total parameters")
        
        # Test model summary
        summary = analyzer.get_model_summary()
        print("✓ Model summary generated successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Analysis tools test failed: {e}")
        return False


def run_comprehensive_setup_test() -> bool:
    """Run all setup tests."""
    print("Hybrid VGG-ViT Setup Test")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Python version
    if not check_python_version():
        all_tests_passed = False
    
    print()
    
    # Test 2: Dependencies
    installed, missing = check_dependencies()
    if missing:
        print(f"\n⚠ Missing dependencies: {missing}")
        print("Install with: pip install -r requirements.txt")
        all_tests_passed = False
    
    print()
    
    # Test 3: PyTorch installation
    torch_info = check_torch_installation()
    if not torch_info:
        all_tests_passed = False
    
    # Test 4: Model creation
    if not test_model_creation():
        all_tests_passed = False
    
    # Test 5: Training utilities
    if not test_training_utilities():
        all_tests_passed = False
    
    # Test 6: Analysis tools
    if not test_analysis_tools():
        all_tests_passed = False
    
    # Final summary
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nYour setup is ready. You can now:")
        print("1. Run 'python example_usage.py' for demonstrations")
        print("2. Run 'python test_model.py' for comprehensive testing")
        print("3. Run 'python train_hybrid_model.py' to start training")
    else:
        print("❌ SOME TESTS FAILED")
        print("\nTroubleshooting steps:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (requires >= 3.8)")
        print("3. Verify PyTorch installation")
        print("4. Check the troubleshooting section in README.md")
    
    return all_tests_passed


if __name__ == "__main__":
    success = run_comprehensive_setup_test()
    sys.exit(0 if success else 1)
