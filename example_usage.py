"""
Example usage of the Hybrid VGG-ViT model with comprehensive error handling.

This script demonstrates how to:
1. <PERSON>reate and initialize the model with validation
2. Perform inference on sample data with error handling
3. Analyze model architecture
4. Visualize feature maps
5. Fine-tune the model
6. Handle common configuration errors
7. Generate sample datasets for testing
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os
import warnings
from typing import Dict, List, Tuple, Any

from hybrid_vgg_vit import (
    create_hybrid_vgg_vit,
    create_model_from_config,
    get_recommended_configs,
    HybridVGGViT,
    ConfigurationError,
    DimensionError
)
from model_utils import ModelAnalyzer, InferenceUtils
from train_hybrid_model import ModelTrainer, create_sample_data_loaders
from test_model import ModelTester


def demo_model_creation():
    """Demonstrate model creation with different configurations and error handling."""
    print("=== Model Creation Demo with Error Handling ===")

    # Test recommended configurations
    configs = get_recommended_configs()
    successful_models = {}

    for config_name, config_params in configs.items():
        try:
            print(f"\nTesting {config_name} configuration:")
            print(f"  Description: {config_params['description']}")

            model = create_model_from_config(config_name, num_classes=1000)
            param_count = sum(p.numel() for p in model.parameters())

            successful_models[config_name] = model
            print(f"  ✓ Success: {param_count:,} parameters")

        except (ConfigurationError, DimensionError) as e:
            print(f"  ✗ Configuration error: {e}")
        except Exception as e:
            print(f"  ✗ Unexpected error: {e}")

    # Demonstrate common configuration errors
    print(f"\n--- Common Configuration Errors ---")

    # Error 1: embed_dim not divisible by num_heads
    try:
        model = create_hybrid_vgg_vit(
            num_classes=1000,
            embed_dim=770,  # Not divisible by 12
            num_heads=12
        )
    except ConfigurationError as e:
        print(f"✓ Caught expected error: {e}")

    # Error 2: Invalid dimensions
    try:
        model = create_hybrid_vgg_vit(
            num_classes=0,  # Invalid
            embed_dim=768
        )
    except ConfigurationError as e:
        print(f"✓ Caught expected error: {e}")

    # Return a working model for further demos
    if successful_models:
        return list(successful_models.values())[0]
    else:
        # Fallback to a simple working configuration
        print("\nFalling back to minimal working configuration...")
        return create_hybrid_vgg_vit(
            num_classes=10,
            embed_dim=384,
            num_heads=6,
            num_transformer_layers=2
        )


def demo_inference():
    """Demonstrate inference on sample data with comprehensive error handling."""
    print("\n=== Inference Demo with Error Handling ===")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    if device.type == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    try:
        # Create model with validation
        model = create_hybrid_vgg_vit(num_classes=1000, validate_config=True)
        model.to(device)
        model.eval()

        print(f"Model created with {sum(p.numel() for p in model.parameters()):,} parameters")

    except (ConfigurationError, DimensionError) as e:
        print(f"Model creation failed: {e}")
        print("Trying with smaller configuration...")

        model = create_hybrid_vgg_vit(
            num_classes=100,
            embed_dim=384,
            num_heads=6,
            num_transformer_layers=4
        )
        model.to(device)
        model.eval()

    # Test different input scenarios
    test_cases = [
        {"batch_size": 1, "description": "Single image"},
        {"batch_size": 4, "description": "Small batch"},
        {"batch_size": 8, "description": "Medium batch"}
    ]

    successful_tests = 0

    for test_case in test_cases:
        try:
            batch_size = test_case["batch_size"]
            description = test_case["description"]

            print(f"\nTesting {description} (batch_size={batch_size}):")

            # Create sample input with proper validation
            sample_input = torch.randn(batch_size, 3, 224, 224).to(device)

            # Validate input dimensions
            if sample_input.shape[1:] != (3, 224, 224):
                raise DimensionError(f"Invalid input shape: {sample_input.shape}")

            # Forward pass with timing
            with torch.no_grad():
                if device.type == 'cuda':
                    torch.cuda.synchronize()
                    start_time = torch.cuda.Event(enable_timing=True)
                    end_time = torch.cuda.Event(enable_timing=True)

                    start_time.record()
                    output = model(sample_input)
                    end_time.record()

                    torch.cuda.synchronize()
                    inference_time = start_time.elapsed_time(end_time)
                else:
                    import time
                    start_time = time.time()
                    output = model(sample_input)
                    end_time = time.time()
                    inference_time = (end_time - start_time) * 1000

            # Validate output
            expected_shape = (batch_size, model.head.out_features)
            if output.shape != expected_shape:
                raise DimensionError(f"Output shape mismatch: expected {expected_shape}, got {output.shape}")

            print(f"  ✓ Success: {inference_time:.2f} ms, output shape {output.shape}")

            # Show predictions for first sample
            probabilities = torch.softmax(output, dim=1)
            top_probs, top_indices = torch.topk(probabilities, k=3, dim=1)

            print(f"  Top 3 predictions for first sample:")
            for i in range(3):
                print(f"    Class {top_indices[0][i].item()}: {top_probs[0][i].item():.4f}")

            successful_tests += 1

        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"  ✗ CUDA out of memory for batch_size={batch_size}")
                print(f"    Try reducing batch size or using a smaller model")
            else:
                print(f"  ✗ Runtime error: {e}")
        except DimensionError as e:
            print(f"  ✗ Dimension error: {e}")
        except Exception as e:
            print(f"  ✗ Unexpected error: {e}")

    print(f"\nInference testing completed: {successful_tests}/{len(test_cases)} tests successful")
    return model


def demo_model_analysis():
    """Demonstrate model analysis capabilities."""
    print("\n=== Model Analysis Demo ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = create_hybrid_vgg_vit(num_classes=1000)
    
    # Create analyzer
    analyzer = ModelAnalyzer(model, device)
    
    # Print model summary
    print(analyzer.get_model_summary())
    
    # Analyze complexity
    complexity = analyzer.analyze_model_complexity()
    print(f"\nDetailed Parameter Analysis:")
    print(f"VGG Features: {complexity['parameters']['vgg_features']:,} parameters")
    print(f"Transformer: {complexity['parameters']['transformer']:,} parameters")
    print(f"Model Size: {complexity['model_size_mb']:.1f} MB")
    
    return analyzer


def demo_feature_visualization():
    """Demonstrate feature map visualization."""
    print("\n=== Feature Visualization Demo ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = create_hybrid_vgg_vit(num_classes=1000)
    analyzer = ModelAnalyzer(model, device)
    
    # Create a sample image (random noise for demo)
    sample_image = torch.randn(1, 3, 224, 224)
    
    # Get feature maps from different VGG blocks
    for i, block_name in enumerate(['Block 1', 'Block 2', 'Block 3', 'Block 4']):
        feature_maps = analyzer.visualize_feature_maps(sample_image, layer_idx=i)
        print(f"{block_name} feature maps shape: {feature_maps.shape}")
    
    # Plot feature maps from the last block
    print("Plotting feature maps from the last VGG block...")
    try:
        fig = analyzer.plot_feature_maps(sample_image, num_maps=16)
        plt.savefig('feature_maps_demo.png', dpi=150, bbox_inches='tight')
        print("Feature maps saved as 'feature_maps_demo.png'")
    except Exception as e:
        print(f"Could not create plot: {e}")
    
    return analyzer


def demo_transfer_learning():
    """Demonstrate transfer learning setup."""
    print("\n=== Transfer Learning Demo ===")
    
    # Create model for transfer learning
    model = create_hybrid_vgg_vit(num_classes=1000)  # Pre-trained on ImageNet
    
    # Modify for new task (e.g., 10 classes)
    num_new_classes = 10
    model.head = nn.Linear(model.head.in_features, num_new_classes)
    
    print(f"Modified model for {num_new_classes} classes")
    
    # Freeze VGG features for transfer learning
    for param in model.vgg_features.parameters():
        param.requires_grad = False
    
    # Count trainable parameters
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    
    print(f"Trainable parameters: {trainable_params:,} / {total_params:,}")
    print(f"Frozen parameters: {total_params - trainable_params:,}")
    
    return model


def demo_model_comparison():
    """Compare different model configurations."""
    print("\n=== Model Comparison Demo ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create different model variants
    models = {
        'Small (4 layers)': create_hybrid_vgg_vit(num_classes=1000, embed_dim=384, num_transformer_layers=4),
        'Medium (6 layers)': create_hybrid_vgg_vit(num_classes=1000, embed_dim=768, num_transformer_layers=6),
        'Large (12 layers)': create_hybrid_vgg_vit(num_classes=1000, embed_dim=768, num_transformer_layers=12),
    }
    
    # Compare model sizes and inference times
    sample_input = torch.randn(1, 3, 224, 224).to(device)
    
    print(f"{'Model':<20} {'Parameters':<15} {'Size (MB)':<12} {'Inference (ms)':<15}")
    print("-" * 65)
    
    for name, model in models.items():
        model.to(device)
        model.eval()
        
        # Count parameters
        param_count = sum(p.numel() for p in model.parameters())
        model_size = param_count * 4 / (1024 * 1024)  # 4 bytes per float32
        
        # Measure inference time
        with torch.no_grad():
            # Warmup
            for _ in range(5):
                _ = model(sample_input)
            
            if device.type == 'cuda':
                torch.cuda.synchronize()
                start_time = torch.cuda.Event(enable_timing=True)
                end_time = torch.cuda.Event(enable_timing=True)
                
                start_time.record()
                for _ in range(10):
                    _ = model(sample_input)
                end_time.record()
                
                torch.cuda.synchronize()
                avg_time = start_time.elapsed_time(end_time) / 10
            else:
                import time
                start_time = time.time()
                for _ in range(10):
                    _ = model(sample_input)
                end_time = time.time()
                avg_time = (end_time - start_time) * 1000 / 10  # Convert to ms
        
        print(f"{name:<20} {param_count:<15,} {model_size:<12.1f} {avg_time:<15.2f}")


def create_sample_dataset():
    """Create a small sample dataset for testing."""
    print("\n=== Creating Sample Dataset ===")
    
    import os
    
    # Create directory structure
    data_dir = "sample_data"
    for split in ['train', 'val']:
        for class_name in ['class_0', 'class_1', 'class_2']:
            os.makedirs(f"{data_dir}/{split}/{class_name}", exist_ok=True)
    
    # Generate random images
    transform = transforms.ToPILImage()
    
    for split in ['train', 'val']:
        num_samples = 20 if split == 'train' else 5
        for class_idx in range(3):
            for sample_idx in range(num_samples):
                # Generate random image
                random_image = torch.randint(0, 256, (3, 224, 224), dtype=torch.uint8)
                pil_image = transform(random_image)
                
                # Save image
                filename = f"{data_dir}/{split}/class_{class_idx}/sample_{sample_idx:03d}.jpg"
                pil_image.save(filename)
    
    print(f"Sample dataset created in '{data_dir}' directory")
    print("Structure:")
    print("sample_data/")
    print("  train/")
    print("    class_0/ (20 images)")
    print("    class_1/ (20 images)")
    print("    class_2/ (20 images)")
    print("  val/")
    print("    class_0/ (5 images)")
    print("    class_1/ (5 images)")
    print("    class_2/ (5 images)")
    
    return data_dir


def main():
    """Run all demos."""
    print("Hybrid VGG-ViT Model Demo")
    print("=" * 50)
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    try:
        # Demo 1: Model creation
        model = demo_model_creation()
        
        # Demo 2: Inference
        model = demo_inference()
        
        # Demo 3: Model analysis
        analyzer = demo_model_analysis()
        
        # Demo 4: Feature visualization
        analyzer = demo_feature_visualization()
        
        # Demo 5: Transfer learning
        transfer_model = demo_transfer_learning()
        
        # Demo 6: Model comparison
        demo_model_comparison()
        
        # Demo 7: Create sample dataset
        data_dir = create_sample_dataset()
        
        print("\n" + "=" * 50)
        print("All demos completed successfully!")
        print("\nNext steps:")
        print("1. Use the sample dataset to test training with train_hybrid_model.py")
        print("2. Modify the model architecture for your specific use case")
        print("3. Implement custom data loaders for your dataset")
        print("4. Experiment with different hyperparameters")
        
    except Exception as e:
        print(f"Error during demo: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
